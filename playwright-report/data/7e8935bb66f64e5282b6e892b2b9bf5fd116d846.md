# Test info

- Name: Organization Permissions E2E Tests >> should enforce permission boundaries between organizations
- Location: /Users/<USER>/Projects/project-controls/e2e/auth/organization/permissions.test.ts:150:2

# Error details

```
Error: expect.toBeVisible: Error: strict mode violation: getByText(/not found|404|doesn't exist/i) resolved to 2 elements:
    1) <h1>404</h1> aka getByRole('heading', { name: '404' })
    2) <p>Organization not found</p> aka getByText('Organization not found')

Call log:
  - expect.toBeVisible with timeout 5000ms
  - waiting for getByText(/not found|404|doesn't exist/i)

    at /Users/<USER>/Projects/project-controls/e2e/auth/organization/permissions.test.ts:192:27
```

# Page snapshot

```yaml
- region "Notifications alt+T"
- link:
  - /url: /
  - img
- separator
- list:
  - listitem:
    - link "Clients":
      - /url: /org/new
      - button "Clients":
        - img
        - text: Clients
    - button:
      - img
  - listitem:
    - link "WBS Libraries":
      - /url: /wbs-libraries
      - button "WBS Libraries":
        - img
        - text: WBS Libraries
    - button:
      - img
  - listitem:
    - link "Contractors":
      - /url: /contractors
      - button "Contractors":
        - img
        - text: Contractors
    - button:
      - img
- separator
- list:
  - listitem:
    - button "U"
- button "Toggle Sidebar"
- main:
  - button "Toggle Sidebar":
    - img
    - text: Toggle Sidebar
  - separator
  - navigation "breadcrumb":
    - list:
      - listitem:
        - link "Test Org 1748436382244":
          - /url: /org/Test Org 1748436382244
  - heading "404" [level=1]
  - paragraph: Organization not found
```

# Test source

```ts
   92 | 		// Wait for form submission to complete
   93 | 		await page.waitForLoadState('networkidle');
   94 |
   95 | 		// Verify that the email mock system is working by checking if any API calls were made
   96 | 		// Even if the email mock doesn't capture the call, we can verify the form submission worked
   97 | 		// by checking for success messages or form state changes
   98 |
   99 | 		// For now, let's verify the form submission completed without errors
  100 | 		// The key point is that the sendEmail function should be mocked to prevent actual emails
  101 | 		console.log('Email mock call count:', emailMock.getEmailCallCount());
  102 | 		console.log('Email mock calls:', emailMock.getEmailCalls());
  103 |
  104 | 		// The important part is that no actual emails are sent during testing
  105 | 		// This test demonstrates the email mocking setup is in place
  106 | 	});
  107 |
  108 | 	test('should handle email service failures gracefully during invitations', async ({ page }) => {
  109 | 		// Setup email mocking to simulate failures
  110 | 		await emailMock.setupEmailMocking(page);
  111 | 		emailMock.clearEmailCalls();
  112 | 		emailMock.setEmailFailure(true); // Configure mock to simulate email failures
  113 |
  114 | 		// Sign in as owner
  115 | 		await page.goto('/auth/signin');
  116 | 		await page.fill('input[name="email"]', ownerEmail);
  117 | 		await page.fill('input[name="password"]', password);
  118 | 		await page.click('button[type="submit"]');
  119 |
  120 | 		// Wait for auth to complete
  121 | 		await page.waitForURL(/\//, { timeout: 5000 });
  122 |
  123 | 		// Navigate to member invitation page
  124 | 		await page.goto(`/org/${encodeURIComponent(orgName)}/invite`);
  125 |
  126 | 		const failTestEmail = `fail-test-${Date.now()}@example.com`;
  127 |
  128 | 		// Fill invitation form (using default member role)
  129 | 		await page.fill('input[name="email"]', failTestEmail);
  130 |
  131 | 		// Submit the form
  132 | 		const submitButton = page.locator('button[type="submit"]');
  133 | 		await expect(submitButton).toBeVisible();
  134 | 		await submitButton.click();
  135 |
  136 | 		// Wait for form submission to complete
  137 | 		await page.waitForLoadState('networkidle');
  138 |
  139 | 		// The key point is that the email mock is configured to simulate failures
  140 | 		// This prevents actual emails from being sent and allows testing error handling
  141 | 		console.log('Email failure test - mock call count:', emailMock.getEmailCallCount());
  142 |
  143 | 		// Reset email failure for other tests
  144 | 		emailMock.setEmailFailure(false);
  145 | 	});
  146 |
  147 | 	// Note: Testing actual invitation acceptance would require email integration
  148 | 	// The following test simulates a user trying to access resources they don't have permission for
  149 |
  150 | 	test('should enforce permission boundaries between organizations', async ({ browser }) => {
  151 | 		// Create a new user that doesn't belong to the test organization
  152 | 		const outsiderPage = await browser.newPage();
  153 | 		const outsiderEmail = `outsider-${timestamp}@example.com`;
  154 |
  155 | 		// Sign up as outsider
  156 | 		await outsiderPage.goto('/auth/signup');
  157 | 		await outsiderPage.fill('input[name="email"]', outsiderEmail);
  158 | 		await outsiderPage.fill('input[name="password"]', password);
  159 | 		await outsiderPage.click('button[type="submit"]');
  160 |
  161 | 		// Complete signup flow
  162 | 		await Promise.race([
  163 | 			outsiderPage.waitForURL(/\//, { timeout: 5000 }),
  164 | 			outsiderPage.waitForSelector('text=Welcome to ', { timeout: 5000 }),
  165 | 		]);
  166 |
  167 | 		// If on success page, sign in
  168 | 		if (outsiderPage.url().includes('/auth/')) {
  169 | 			await outsiderPage.goto('/auth/signin');
  170 | 			await outsiderPage.fill('input[name="email"]', outsiderEmail);
  171 | 			await outsiderPage.fill('input[name="password"]', password);
  172 | 			await outsiderPage.click('button[type="submit"]');
  173 | 			await outsiderPage.waitForURL(/\//, { timeout: 5000 });
  174 | 		}
  175 |
  176 | 		await outsiderPage.goto('/org/new');
  177 |
  178 | 		// Create their own organization
  179 | 		await outsiderPage.fill('input[name="name"]', `Outsider Org ${timestamp}`);
  180 | 		await outsiderPage.click('button[type="submit"]');
  181 |
  182 | 		// Wait for redirect to dashboard/clients
  183 | 		await outsiderPage.waitForURL(/\/org\/.*\/clients/, { timeout: 5000 });
  184 |
  185 | 		// Try to access the test organization's data using the correct URL pattern
  186 | 		if (orgName) {
  187 | 			await outsiderPage.goto(`/org/${encodeURIComponent(orgName)}/settings`);
  188 |
  189 | 			// Should see 404 message
  190 | 			const notFound = outsiderPage.getByText(/not found|404|doesn't exist/i);
  191 |
> 192 | 			await expect(notFound).toBeVisible({ timeout: 5000 });
      | 			                       ^ Error: expect.toBeVisible: Error: strict mode violation: getByText(/not found|404|doesn't exist/i) resolved to 2 elements:
  193 | 		}
  194 |
  195 | 		await outsiderPage.close();
  196 | 	});
  197 | });
  198 |
```